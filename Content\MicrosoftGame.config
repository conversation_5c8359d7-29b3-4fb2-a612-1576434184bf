<?xml version="1.0" encoding="utf-8"?>
<Game
  configVersion="1">
  <!-- For more information on MicrosoftGame.config elements, please refer to online GDK documentation found at https://aka.ms/GDK_MSGC or the MicrosoftGame.config topic in the System section of the offline GDK documentation. -->
  <Identity
    Name="38985CA0.CallofDutyWWIIPCMS"
    Publisher="CN=07A9AC0F-5502-4D92-BA69-01D5D39D1E92"
    ResourceId="WW"
    Version="********" />
  <ShellVisuals
    DefaultDisplayName="Call of Duty WWII"
    PublisherDisplayName="Activision Publishing Inc."
    StoreLogo="StoreLogo.png"
    Square150x150Logo="GraphicsLogo.png"
    Square44x44Logo="SmallLogo.png"
    Square480x480Logo="LargeLogo.png"
    Description="Call of Duty WWII PC MS"
    ForegroundText="light"
    BackgroundColor="#000040"
    SplashScreenImage="SplashScreen.png" />
  <ExecutableList>
    <Executable
      Name="s2_sp64_ship.exe"
      TargetDeviceFamily="PC"
      Id="GameSP"
      OverrideDisplayName="Call of Duty®: WWII" />
    <Executable
      Name="s2_mp64_ship.exe"
      TargetDeviceFamily="PC"
      Id="GameMP"
      OverrideDisplayName="Call of Duty®: WWII - Multiplayer"
      OverrideLogo="GraphicsLogo_mp.png"
      OverrideSquare44x44Logo="SmallLogo_mp.png"
      OverrideSquare480x480Logo="LargeLogo_mp.png" />
  </ExecutableList>
	<Resources>
		<Resource Language="en-us" />
		<Resource Language="fr-fr" />
		<Resource Language="es-es" />
		<Resource Language="it-it" />
		<Resource Language="ko-kr" />
		<Resource Language="pl-pl" />
		<Resource Language="pt-br" />
		<Resource Language="zh-cn" />
		<Resource Language="zh-tw" />
	</Resources>
  <StoreId>9P3QS4LX8BM9</StoreId>
  <MSAAppId>a87315a1-b80d-4b5e-a041-40ff83a58e7a</MSAAppId>
  <TitleId>6e704c33</TitleId>
  <DesktopRegistration>
    <DependencyList>
      <KnownDependency Name="VC14"/>
	  <KnownDependency Name="DX11"/>
    </DependencyList>
    <MultiplayerProtocol
      Executable="s2_mp64_ship.exe">true</MultiplayerProtocol>
  </DesktopRegistration>
  <AdvancedUserModel>true</AdvancedUserModel>
</Game>