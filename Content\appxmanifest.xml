<?xml version="1.0" encoding="UTF-8"?>
<Package xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10" xmlns:desktop6="http://schemas.microsoft.com/appx/manifest/desktop/windows10/6" xmlns:desktop="http://schemas.microsoft.com/appx/manifest/desktop/windows10" xmlns:uap3="http://schemas.microsoft.com/appx/manifest/uap/windows10/3" xmlns:wincap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/windowscapabilities" xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities" IgnorableNamespaces="uap uap3 desktop desktop6 wincap rescap" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
  <Identity Name="38985CA0.CallofDutyWWIIPCMS" Publisher="CN=07A9AC0F-5502-4D92-BA69-01D5D39D1E92" Version="********" ResourceId="WW" ProcessorArchitecture="x64" />
  <Properties>
    <DisplayName>Call of Duty WWII</DisplayName>
    <PublisherDisplayName>Activision Publishing Inc.</PublisherDisplayName>
    <Logo>StoreLogo.png</Logo>
    <Description>Call of Duty WWII PC MS</Description>
    <desktop6:RegistryWriteVirtualization>disabled</desktop6:RegistryWriteVirtualization>
    <desktop6:FileSystemWriteVirtualization>disabled</desktop6:FileSystemWriteVirtualization>
  </Properties>
  <Dependencies>
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.18362.0" MaxVersionTested="10.0.18362.0" />
    <PackageDependency Name="Microsoft.VCLibs.140.00.UWPDesktop" MinVersion="14.0.33728.0" Publisher="CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US" />
    <PackageDependency Name="Microsoft.DirectXRuntime" MinVersion="9.29.1974.0" Publisher="CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US" />
  </Dependencies>
  <Resources>
    <Resource Language="en-us" />
    <Resource Language="fr-fr" />
    <Resource Language="es-es" />
    <Resource Language="it-it" />
    <Resource Language="ko-kr" />
    <Resource Language="pl-pl" />
    <Resource Language="pt-br" />
    <Resource Language="zh-cn" />
    <Resource Language="zh-tw" />
  </Resources>
  <Applications>
    <Application Id="GameSP" Executable="GameLaunchHelper.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements DisplayName="Call of Duty®: WWII" Square150x150Logo="GraphicsLogo.png" Square44x44Logo="SmallLogo.png" Description="Call of Duty WWII PC MS" ForegroundText="light" BackgroundColor="#000040">
        <uap:SplashScreen Image="SplashScreen.png" />
      </uap:VisualElements>
    </Application>
    <Application Id="GameMP" Executable="GameLaunchHelper.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements DisplayName="Call of Duty®: WWII - Multiplayer" Square150x150Logo="GraphicsLogo_mp.png" Square44x44Logo="SmallLogo_mp.png" Description="Call of Duty WWII PC MS" ForegroundText="light" BackgroundColor="#000040">
        <uap:SplashScreen Image="SplashScreen.png" />
      </uap:VisualElements>
      <Extensions>
        <uap:Extension Category="windows.protocol">
          <uap:Protocol Name="ms-xbl-6e704c33" />
        </uap:Extension>
        <uap:Extension Category="windows.protocol">
          <uap:Protocol Name="ms-xbl-multiplayer" />
        </uap:Extension>
      </Extensions>
    </Application>
  </Applications>
  <Capabilities>
    <Capability Name="internetClient" />
    <rescap:Capability Name="runFullTrust" />
    <rescap:Capability Name="appLicensing" />
    <rescap:Capability Name="unvirtualizedResources" />
  </Capabilities>
</Package>