// generated by Sledgehammer Games - do not modify
unbindall
bind TAB "togglescores"
bind <PERSON>SC<PERSON><PERSON> "togglemenu"
bind <PERSON>AC<PERSON> "+gostand"
bind 1 "+weapnext"
bind 2 "+actionslot 3"
bind 3 "+actionslot 5"
bind 4 "+actionslot 6"
bind 5 "+actionslot 7"
bind 6 "+actionslot 4"
bind 7 "+actionslot 8"
bind A "+moveleft"
bind C "+stance"
bind D "+moveright"
bind E "+smoke"
bind F "+activate"
bind G "+frag"
bind M "+actionslot 2"
bind N "+actionslot 1"
bind Q "+melee_zoom"
bind R "+reload"
bind S "+back"
bind T "chatmodepublic"
bind V "+combat_roles"
bind W "+forward"
bind X "+inspectweapon"
bind Y "chatmodeteam"
bind Z "+talk"
bind CAPSLOCK "+mlook"
bind UPARROW "+lookup"
bind DOWNARROW "+lookdown"
bind LEFTARROW "+left"
bind RIGHTARROW "+right"
bind CTRL "toggleprone"
bind SHIFT "+breath_sprint"
bind MOUSE1 "+attack"
bind MOUSE2 "+speed_throw"
bind MWHEE<PERSON>OW<PERSON> "+weapnext"
bind MWHEE<PERSON><PERSON> "+weapnext"
